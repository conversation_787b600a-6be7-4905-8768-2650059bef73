<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Start session
session_start();

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login.php');
}

// Redirect based on user role
if (has_role('admin')) {
    redirect('admin/');
} elseif (has_role('vendor')) {
    redirect('vendor/');
}

// Client dashboard
$page_title = "डैशबोर्ड - " . SITE_NAME;
$user_id = $_SESSION['user_id'];

// Get user's projects
$projects = get_user_projects($user_id, 5);

// Get user's recent orders
try {
    $stmt = $pdo->prepare("SELECT o.*, p.title as project_title FROM orders o 
                          LEFT JOIN projects p ON o.project_id = p.id 
                          WHERE o.client_id = ? 
                          ORDER BY o.created_at DESC LIMIT 5");
    $stmt->execute([$user_id]);
    $recent_orders = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_orders = [];
}

// Get dashboard stats
try {
    $stmt = $pdo->prepare("SELECT 
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_projects,
        COUNT(CASE WHEN status = 'in-progress' THEN 1 END) as active_projects,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
        COUNT(*) as total_projects
        FROM projects WHERE client_id = ?");
    $stmt->execute([$user_id]);
    $project_stats = $stmt->fetch();
    
    $stmt = $pdo->prepare("SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN payment_status = 'completed' THEN total_amount ELSE 0 END) as total_paid
        FROM orders WHERE client_id = ?");
    $stmt->execute([$user_id]);
    $order_stats = $stmt->fetch();
} catch (PDOException $e) {
    $project_stats = ['pending_projects' => 0, 'active_projects' => 0, 'completed_projects' => 0, 'total_projects' => 0];
    $order_stats = ['total_orders' => 0, 'total_paid' => 0];
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard-page">
    <!-- Dashboard Header -->
    <header class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-brand">
                        <a href="../index.php">
                            <img src="../assets/images/logo.png" alt="<?php echo SITE_NAME; ?>" height="40">
                            <span><?php echo SITE_NAME; ?></span>
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-user-menu">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> प्रोफाइल</a></li>
                                <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> सेटिंग्स</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> लॉगआउट</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Dashboard Content -->
    <div class="dashboard-container">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-lg-3 col-md-4">
                    <div class="dashboard-sidebar">
                        <nav class="sidebar-nav">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link active" href="index.php">
                                        <i class="fas fa-tachometer-alt"></i> डैशबोर्ड
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="projects.php">
                                        <i class="fas fa-project-diagram"></i> मेरे प्रोजेक्ट्स
                                        <?php if ($project_stats['pending_projects'] > 0): ?>
                                        <span class="badge bg-warning"><?php echo $project_stats['pending_projects']; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="orders.php">
                                        <i class="fas fa-shopping-cart"></i> मेरे ऑर्डर्स
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="support.php">
                                        <i class="fas fa-headset"></i> सपोर्ट टिकट्स
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="invoices.php">
                                        <i class="fas fa-file-invoice"></i> इनवॉइसेज
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="profile.php">
                                        <i class="fas fa-user"></i> प्रोफाइल
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        
                        <!-- Quick Actions -->
                        <div class="sidebar-actions mt-4">
                            <h6>त्वरित कार्य</h6>
                            <a href="../services.php" class="btn btn-primary btn-sm w-100 mb-2">
                                <i class="fas fa-plus"></i> नया प्रोजेक्ट
                            </a>
                            <a href="support.php?action=new" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-question-circle"></i> सपोर्ट टिकट
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content -->
                <div class="col-lg-9 col-md-8">
                    <div class="dashboard-main">
                        <!-- Welcome Section -->
                        <div class="welcome-section mb-4">
                            <h1>स्वागत है, <?php echo $_SESSION['full_name']; ?>!</h1>
                            <p class="text-muted">आपके प्रोजेक्ट्स और ऑर्डर्स का ओवरव्यू</p>
                        </div>
                        
                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-icon bg-primary">
                                        <i class="fas fa-project-diagram"></i>
                                    </div>
                                    <div class="stats-content">
                                        <h3><?php echo $project_stats['total_projects']; ?></h3>
                                        <p>कुल प्रोजेक्ट्स</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-icon bg-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stats-content">
                                        <h3><?php echo $project_stats['pending_projects']; ?></h3>
                                        <p>पेंडिंग प्रोजेक्ट्स</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-icon bg-info">
                                        <i class="fas fa-spinner"></i>
                                    </div>
                                    <div class="stats-content">
                                        <h3><?php echo $project_stats['active_projects']; ?></h3>
                                        <p>चालू प्रोजेक्ट्स</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-icon bg-success">
                                        <i class="fas fa-rupee-sign"></i>
                                    </div>
                                    <div class="stats-content">
                                        <h3><?php echo format_currency($order_stats['total_paid']); ?></h3>
                                        <p>कुल भुगतान</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Recent Projects -->
                            <div class="col-lg-8 mb-4">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-project-diagram"></i> हाल के प्रोजेक्ट्स</h5>
                                        <a href="projects.php" class="btn btn-sm btn-outline-primary">सभी देखें</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($projects)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>प्रोजेक्ट</th>
                                                        <th>पैकेज</th>
                                                        <th>स्थिति</th>
                                                        <th>तारीख</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($projects as $project): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($project['title']); ?></strong>
                                                            <br><small class="text-muted"><?php echo truncate_text($project['description'], 50); ?></small>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-info"><?php echo ucfirst($project['package_type']); ?></span>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $status_colors = [
                                                                'pending' => 'warning',
                                                                'in-progress' => 'info',
                                                                'completed' => 'success',
                                                                'cancelled' => 'danger'
                                                            ];
                                                            $status_labels = [
                                                                'pending' => 'पेंडिंग',
                                                                'in-progress' => 'चालू',
                                                                'completed' => 'पूर्ण',
                                                                'cancelled' => 'रद्द'
                                                            ];
                                                            ?>
                                                            <span class="badge bg-<?php echo $status_colors[$project['status']]; ?>">
                                                                <?php echo $status_labels[$project['status']]; ?>
                                                            </span>
                                                        </td>
                                                        <td><?php echo format_date($project['created_at']); ?></td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                            <h6>कोई प्रोजेक्ट नहीं मिला</h6>
                                            <p class="text-muted">अपना पहला प्रोजेक्ट शुरू करें</p>
                                            <a href="../services.php" class="btn btn-primary">नया प्रोजेक्ट</a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recent Orders -->
                            <div class="col-lg-4 mb-4">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-shopping-cart"></i> हाल के ऑर्डर्स</h5>
                                        <a href="orders.php" class="btn btn-sm btn-outline-primary">सभी देखें</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($recent_orders)): ?>
                                        <?php foreach ($recent_orders as $order): ?>
                                        <div class="order-item">
                                            <div class="order-info">
                                                <strong>#<?php echo $order['order_number']; ?></strong>
                                                <br><small><?php echo $order['project_title'] ?? 'N/A'; ?></small>
                                            </div>
                                            <div class="order-amount">
                                                <?php echo format_currency($order['total_amount']); ?>
                                                <br><small class="badge bg-<?php echo $order['payment_status'] === 'completed' ? 'success' : 'warning'; ?>">
                                                    <?php echo $order['payment_status']; ?>
                                                </small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                        <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">कोई ऑर्डर नहीं मिला</p>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-bolt"></i> त्वरित कार्य</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <a href="../services.php" class="quick-action-btn">
                                                    <i class="fas fa-plus-circle"></i>
                                                    <span>नया प्रोजेक्ट</span>
                                                </a>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <a href="support.php?action=new" class="quick-action-btn">
                                                    <i class="fas fa-headset"></i>
                                                    <span>सपोर्ट टिकट</span>
                                                </a>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <a href="invoices.php" class="quick-action-btn">
                                                    <i class="fas fa-file-invoice"></i>
                                                    <span>इनवॉइसेज</span>
                                                </a>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <a href="profile.php" class="quick-action-btn">
                                                    <i class="fas fa-user-edit"></i>
                                                    <span>प्रोफाइल अपडेट</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
