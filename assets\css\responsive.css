/* Responsive Styles for WebsiteDeveloper0002 */

/* Extra Large Devices (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-header h2 {
        font-size: 3rem;
    }
}

/* Large Devices (992px and up) */
@media (min-width: 992px) {
    .hero-section {
        padding: 120px 0;
    }
    
    .services-overview,
    .why-choose-us,
    .contact-section {
        padding: 100px 0;
    }
    
    .footer-main {
        padding: 80px 0 60px;
    }
}

/* Medium Devices (768px and up) */
@media (min-width: 768px) {
    .newsletter-form {
        margin-left: auto;
    }
    
    .payment-icons {
        justify-content: flex-end;
    }
    
    .social-media {
        text-align: left;
    }
    
    .payment-methods {
        text-align: right;
    }
}

/* Small Devices (576px and up) */
@media (min-width: 576px) {
    .hero-buttons .btn {
        margin-right: 20px;
    }
    
    .service-buttons .btn {
        width: auto;
        margin: 5px 10px;
    }
    
    .contact-form {
        padding: 50px;
    }
}

/* Extra Small Devices (less than 576px) */
@media (max-width: 575.98px) {
    /* Typography */
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    /* Header */
    .top-bar {
        padding: 5px 0;
        font-size: 12px;
    }
    
    .top-bar .contact-info span {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }
    
    .top-bar .social-links {
        margin-top: 10px;
    }
    
    .navbar {
        padding: 10px 0;
    }
    
    .navbar-brand {
        font-size: 20px;
    }
    
    .navbar-brand img {
        height: 35px;
    }
    
    .navbar-nav .nav-link {
        padding: 8px 15px;
        font-size: 16px;
    }
    
    /* Hero Section */
    .hero-section {
        padding: 60px 0;
        text-align: center;
    }
    
    .hero-content {
        margin-bottom: 40px;
    }
    
    .hero-buttons {
        text-align: center;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }
    
    .feature-item {
        justify-content: center;
        text-align: center;
        margin-bottom: 20px;
    }
    
    /* Services */
    .services-overview,
    .why-choose-us,
    .contact-section {
        padding: 60px 0;
    }
    
    .section-header {
        margin-bottom: 40px;
    }
    
    .service-card {
        padding: 30px 20px;
        margin-bottom: 30px;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .service-card .price {
        font-size: 1.75rem;
    }
    
    .service-buttons .btn {
        width: 100%;
        margin: 5px 0;
    }
    
    /* Feature Boxes */
    .feature-box {
        padding: 30px 15px;
        margin-bottom: 30px;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    /* Contact Form */
    .contact-form {
        padding: 30px 20px;
    }
    
    .contact-form .row .col-md-6 {
        margin-bottom: 15px;
    }
    
    /* Footer */
    .newsletter-section {
        padding: 40px 0;
        text-align: center;
    }
    
    .newsletter-content {
        margin-bottom: 30px;
    }
    
    .newsletter-content h3 {
        font-size: 1.5rem;
    }
    
    .newsletter-form {
        margin-left: 0;
    }
    
    .newsletter-form .input-group {
        flex-direction: column;
    }
    
    .newsletter-form .form-control {
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        margin-bottom: 0;
    }
    
    .newsletter-form .btn {
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }
    
    .footer-main {
        padding: 40px 0 30px;
    }
    
    .footer-widget {
        text-align: center;
        margin-bottom: 40px;
    }
    
    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .footer-bottom-info {
        text-align: center;
    }
    
    .footer-bottom-info .row > div {
        margin-bottom: 20px;
    }
    
    .social-media,
    .payment-methods {
        text-align: center;
    }
    
    .payment-icons {
        justify-content: center;
    }
    
    .footer-copyright {
        text-align: center;
    }
    
    .footer-copyright .row > div {
        margin-bottom: 10px;
    }
    
    /* Buttons */
    .whatsapp-float {
        bottom: 15px;
        right: 15px;
    }
    
    .whatsapp-float a {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .back-to-top {
        bottom: 70px;
        right: 15px;
        width: 45px;
        height: 45px;
    }
    
    /* Cookie Consent */
    .cookie-consent {
        padding: 15px 0;
    }
    
    .cookie-consent .row > div {
        margin-bottom: 15px;
        text-align: center;
    }
    
    .cookie-text {
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    /* Breadcrumb */
    .breadcrumb-section {
        padding: 15px 0;
    }
    
    .breadcrumb {
        font-size: 14px;
    }
    
    /* Alerts */
    .alert {
        padding: 12px 15px;
        font-size: 14px;
    }
}

/* Landscape Phones */
@media (max-width: 767.98px) and (orientation: landscape) {
    .hero-section {
        padding: 40px 0;
    }
    
    .services-overview,
    .why-choose-us,
    .contact-section {
        padding: 50px 0;
    }
    
    .newsletter-section {
        padding: 30px 0;
    }
    
    .footer-main {
        padding: 30px 0 20px;
    }
}

/* Print Styles */
@media print {
    .top-bar,
    .navbar,
    .whatsapp-float,
    .back-to-top,
    .cookie-consent,
    .newsletter-section,
    .footer-bottom-info,
    .footer-copyright {
        display: none !important;
    }
    
    .main-footer {
        background: transparent !important;
        color: var(--dark-color) !important;
    }
    
    .footer-title,
    .footer-description,
    .footer-contact p,
    .footer-links a {
        color: var(--dark-color) !important;
    }
    
    .hero-section {
        background: transparent !important;
        padding: 20px 0;
    }
    
    .service-card,
    .feature-box,
    .contact-form {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .btn {
        background: transparent !important;
        color: var(--dark-color) !important;
        border: 1px solid var(--dark-color) !important;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image img,
    .footer-logo img,
    .payment-icons img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .whatsapp-float a {
        animation: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white-color: #1a1a1a;
        --light-color: #2d2d2d;
        --dark-color: #ffffff;
        --secondary-color: #b0b0b0;
    }
    
    body {
        background-color: var(--white-color);
        color: var(--dark-color);
    }
    
    .hero-section {
        background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    }
    
    .service-card,
    .feature-box,
    .contact-form {
        background: var(--light-color);
        border-color: #404040;
    }
    
    .form-control {
        background: var(--light-color);
        border-color: #404040;
        color: var(--dark-color);
    }
    
    .navbar {
        background: var(--white-color) !important;
    }
}
