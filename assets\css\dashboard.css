/* Dashboard Styles for WebsiteDeveloper0002 */

/* Dashboard Layout */
.dashboard-page {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.dashboard-header {
    background: var(--primary-gradient);
    color: var(--white-color);
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.dashboard-brand {
    display: flex;
    align-items: center;
}

.dashboard-brand a {
    display: flex;
    align-items: center;
    color: var(--white-color);
    text-decoration: none;
    font-size: 20px;
    font-weight: 600;
}

.dashboard-brand img {
    margin-right: 10px;
}

.dashboard-user-menu .dropdown-toggle {
    border-color: rgba(255, 255, 255, 0.3);
    color: var(--white-color);
}

.dashboard-user-menu .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Dashboard Container */
.dashboard-container {
    padding: 30px 0;
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.dashboard-sidebar {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
    position: sticky;
    top: 110px;
    max-height: calc(100vh - 130px);
    overflow-y: auto;
}

.sidebar-nav .nav-link {
    color: var(--dark-color);
    padding: 12px 15px;
    border-radius: var(--border-radius);
    margin-bottom: 5px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    background: var(--primary-gradient);
    color: var(--white-color);
    transform: translateX(5px);
}

.sidebar-nav .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-nav .badge {
    font-size: 11px;
    padding: 3px 6px;
}

.sidebar-actions h6 {
    color: var(--secondary-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    padding-left: 15px;
}

/* Main Content */
.dashboard-main {
    padding-left: 20px;
}

.welcome-section h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
}

/* Stats Cards */
.stats-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    display: flex;
    align-items: center;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: var(--white-color);
    font-size: 24px;
}

.stats-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.stats-content p {
    color: var(--secondary-color);
    margin-bottom: 0;
    font-size: 14px;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.dashboard-card .card-header {
    background: var(--light-color);
    border-bottom: 1px solid #e9ecef;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dashboard-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.dashboard-card .card-header i {
    margin-right: 8px;
    color: var(--primary-color);
}

.dashboard-card .card-body {
    padding: 25px;
}

/* Table Styles */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px;
}

.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Order Items */
.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
    border-bottom: none;
}

.order-info strong {
    color: var(--dark-color);
    font-size: 14px;
}

.order-info small {
    color: var(--secondary-color);
    font-size: 12px;
}

.order-amount {
    text-align: right;
    font-weight: 600;
    color: var(--dark-color);
}

.order-amount small {
    display: block;
    margin-top: 5px;
}

/* Quick Action Buttons */
.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    background: var(--white-color);
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
    height: 120px;
}

.quick-action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.quick-action-btn span {
    font-weight: 500;
    font-size: 14px;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.empty-state h6 {
    color: var(--dark-color);
    margin-bottom: 10px;
}

.empty-state p {
    color: var(--secondary-color);
    margin-bottom: 20px;
}

/* Badges */
.badge {
    font-size: 11px;
    padding: 5px 10px;
    font-weight: 500;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* Alerts in Dashboard */
.dashboard-alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
}

.dashboard-alert.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    border-left: 4px solid var(--info-color);
}

.dashboard-alert.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border-left: 4px solid var(--warning-color);
}

/* Form Styles in Dashboard */
.dashboard-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 15px;
    transition: var(--transition);
}

.dashboard-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dashboard-form .form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .dashboard-main {
        padding-left: 0;
        margin-top: 20px;
    }
    
    .dashboard-sidebar {
        position: static;
        max-height: none;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .quick-action-btn {
        height: 100px;
        padding: 20px 15px;
    }
    
    .quick-action-btn i {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }
}

@media (max-width: 767.98px) {
    .dashboard-container {
        padding: 20px 0;
    }
    
    .dashboard-card .card-header {
        padding: 15px 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .dashboard-card .card-body {
        padding: 20px;
    }
    
    .stats-card {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }
    
    .stats-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .order-amount {
        text-align: left;
        width: 100%;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .table th,
    .table td {
        padding: 10px 8px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dashboard-page {
        background-color: #1a1a1a;
    }
    
    .dashboard-sidebar,
    .dashboard-card,
    .stats-card {
        background: #2d2d2d;
        border-color: #404040;
    }
    
    .dashboard-card .card-header {
        background: #404040;
        border-color: #555;
    }
    
    .table th {
        background: #404040;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    .order-item {
        border-color: #404040;
    }
    
    .quick-action-btn {
        background: #2d2d2d;
        border-color: #404040;
    }
}

/* Print Styles */
@media print {
    .dashboard-header,
    .dashboard-sidebar,
    .sidebar-actions {
        display: none !important;
    }
    
    .dashboard-main {
        padding-left: 0 !important;
    }
    
    .dashboard-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }
    
    .stats-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
