<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

// Redirect if already logged in
if (is_logged_in()) {
    redirect('dashboard/');
}

$page_title = "रजिस्टर करें - " . SITE_NAME;
$error_message = '';
$success_message = '';

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'अमान्य अनुरोध। कृपया पुनः प्रयास करें।';
    } else {
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $username = sanitize_input($_POST['username'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $terms_accepted = isset($_POST['terms_accepted']);
        $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';
        
        // Validation
        $errors = [];
        
        if (empty($first_name)) $errors[] = 'पहला नाम आवश्यक है';
        if (empty($last_name)) $errors[] = 'अंतिम नाम आवश्यक है';
        if (empty($username)) $errors[] = 'यूजरनेम आवश्यक है';
        if (empty($email)) $errors[] = 'ईमेल आवश्यक है';
        if (empty($password)) $errors[] = 'पासवर्ड आवश्यक है';
        
        if (!validate_email($email)) {
            $errors[] = 'वैध ईमेल एड्रेस दर्ज करें';
        }
        
        if ($phone && !validate_phone($phone)) {
            $errors[] = 'वैध फोन नंबर दर्ज करें';
        }
        
        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            $errors[] = 'पासवर्ड कम से कम ' . PASSWORD_MIN_LENGTH . ' अक्षर का होना चाहिए';
        }
        
        if ($password !== $confirm_password) {
            $errors[] = 'पासवर्ड मैच नहीं कर रहे';
        }
        
        if (!$terms_accepted) {
            $errors[] = 'नियम और शर्तों को स्वीकार करना आवश्यक है';
        }
        
        // Verify reCAPTCHA if enabled
        if (defined('RECAPTCHA_SECRET_KEY') && RECAPTCHA_SECRET_KEY) {
            if (empty($recaptcha_response) || !verify_recaptcha($recaptcha_response)) {
                $errors[] = 'reCAPTCHA वेरिफिकेशन आवश्यक है';
            }
        }
        
        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
        } else {
            // Prepare registration data
            $registration_data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $email,
                'phone' => $phone,
                'password' => $password,
                'role' => 'client'
            ];
            
            $result = register_user($registration_data);
            
            if ($result['success']) {
                redirect('login.php?registered=1');
            } else {
                $error_message = $result['message'];
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo SITE_NAME; ?> में रजिस्टर करें और अपना प्रोजेक्ट शुरू करें।">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google reCAPTCHA -->
    <?php if(defined('RECAPTCHA_SITE_KEY') && RECAPTCHA_SITE_KEY): ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <?php endif; ?>
</head>
<body class="auth-page">
    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Register Section -->
    <section class="auth-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="auth-card">
                        <div class="auth-header text-center">
                            <h2>रजिस्टर करें</h2>
                            <p>नया अकाउंट बनाएं</p>
                        </div>
                        
                        <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="auth-form" id="registerForm">
                            <?php echo csrf_token_input(); ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="first_name" class="form-label">पहला नाम *</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" 
                                               placeholder="पहला नाम" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="last_name" class="form-label">अंतिम नाम *</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" 
                                               placeholder="अंतिम नाम" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="username" class="form-label">यूजरनेम *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                           placeholder="यूजरनेम" required>
                                </div>
                                <small class="form-text text-muted">केवल अक्षर, संख्या और अंडरस्कोर</small>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">ईमेल एड्रेस *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           placeholder="आपका ईमेल एड्रेस" required>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="phone" class="form-label">फोन नंबर</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                           placeholder="आपका फोन नंबर">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="password" class="form-label">पासवर्ड *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   placeholder="पासवर्ड" required>
                                            <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">कम से कम <?php echo PASSWORD_MIN_LENGTH; ?> अक्षर</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="confirm_password" class="form-label">पासवर्ड कन्फर्म करें *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   placeholder="पासवर्ड दोबारा दर्ज करें" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Password Strength Indicator -->
                            <div class="password-strength mb-3" style="display: none;">
                                <div class="strength-meter">
                                    <div class="strength-meter-fill"></div>
                                </div>
                                <small class="strength-text"></small>
                            </div>
                            
                            <?php if(defined('RECAPTCHA_SITE_KEY') && RECAPTCHA_SITE_KEY): ?>
                            <div class="form-group mb-3">
                                <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="form-group mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms_accepted" name="terms_accepted" required>
                                    <label class="form-check-label" for="terms_accepted">
                                        मैं <a href="legal/terms-conditions.php" target="_blank">नियम और शर्तों</a> 
                                        और <a href="legal/privacy-policy.php" target="_blank">प्राइवेसी पॉलिसी</a> से सहमत हूं *
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group mb-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-user-plus"></i> रजिस्टर करें
                                </button>
                            </div>
                            
                            <div class="auth-links text-center">
                                <p>पहले से अकाउंट है? <a href="login.php">यहाँ लॉगिन करें</a></p>
                            </div>
                        </form>
                        
                        <div class="auth-divider">
                            <span>या</span>
                        </div>
                        
                        <div class="social-login">
                            <button type="button" class="btn btn-outline-danger w-100 mb-2">
                                <i class="fab fa-google"></i> Google से रजिस्टर करें
                            </button>
                            <button type="button" class="btn btn-outline-primary w-100">
                                <i class="fab fa-facebook-f"></i> Facebook से रजिस्टर करें
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Password strength checker
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthMeter = document.querySelector('.password-strength');
        const strengthFill = document.querySelector('.strength-meter-fill');
        const strengthText = document.querySelector('.strength-text');
        
        if (password.length === 0) {
            strengthMeter.style.display = 'none';
            return;
        }
        
        strengthMeter.style.display = 'block';
        
        let strength = 0;
        let feedback = [];
        
        // Length check
        if (password.length >= 8) strength += 25;
        else feedback.push('कम से कम 8 अक्षर');
        
        // Uppercase check
        if (/[A-Z]/.test(password)) strength += 25;
        else feedback.push('एक बड़ा अक्षर');
        
        // Lowercase check
        if (/[a-z]/.test(password)) strength += 25;
        else feedback.push('एक छोटा अक्षर');
        
        // Number or special character check
        if (/[\d\W]/.test(password)) strength += 25;
        else feedback.push('एक संख्या या विशेष अक्षर');
        
        // Update strength meter
        strengthFill.style.width = strength + '%';
        
        if (strength < 50) {
            strengthFill.style.backgroundColor = '#dc3545';
            strengthText.textContent = 'कमजोर: ' + feedback.join(', ');
            strengthText.style.color = '#dc3545';
        } else if (strength < 75) {
            strengthFill.style.backgroundColor = '#ffc107';
            strengthText.textContent = 'मध्यम';
            strengthText.style.color = '#ffc107';
        } else {
            strengthFill.style.backgroundColor = '#28a745';
            strengthText.textContent = 'मजबूत';
            strengthText.style.color = '#28a745';
        }
    });
    
    // Password confirmation check
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.setCustomValidity('पासवर्ड मैच नहीं कर रहे');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Username validation
    document.getElementById('username').addEventListener('input', function() {
        const username = this.value;
        const regex = /^[a-zA-Z0-9_]+$/;
        
        if (username && !regex.test(username)) {
            this.setCustomValidity('केवल अक्षर, संख्या और अंडरस्कोर की अनुमति है');
        } else {
            this.setCustomValidity('');
        }
    });
    </script>
</body>
</html>
