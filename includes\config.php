<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'websitedeveloper0002_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Site Configuration
define('SITE_NAME', 'WebsiteDeveloper0002');
define('SITE_URL', 'http://localhost/websitedeveloper0002.in');
define('SITE_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');
define('SUPPORT_EMAIL', '<EMAIL>');

// Contact Information
define('CONTACT_PHONE', '+91 98765 43210');
define('CONTACT_WHATSAPP', '919876543210');
define('CONTACT_ADDRESS', 'Your Business Address, City, State, PIN');

// Social Media Links
define('FACEBOOK_URL', 'https://facebook.com/websitedeveloper0002');
define('TWITTER_URL', 'https://twitter.com/websitedeveloper0002');
define('INSTAGRAM_URL', 'https://instagram.com/websitedeveloper0002');
define('LINKEDIN_URL', 'https://linkedin.com/company/websitedeveloper0002');

// Google Services
define('GA_TRACKING_ID', ''); // Google Analytics Tracking ID
define('RECAPTCHA_SITE_KEY', ''); // reCAPTCHA Site Key
define('RECAPTCHA_SECRET_KEY', ''); // reCAPTCHA Secret Key

// Razorpay Configuration
define('RAZORPAY_KEY_ID', ''); // Razorpay Key ID
define('RAZORPAY_KEY_SECRET', ''); // Razorpay Key Secret
define('RAZORPAY_WEBHOOK_SECRET', ''); // Razorpay Webhook Secret

// Email Configuration (SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // Your Gmail address
define('SMTP_PASSWORD', ''); // Your Gmail app password
define('SMTP_ENCRYPTION', 'tls');

// File Upload Configuration
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Pagination
define('POSTS_PER_PAGE', 10);
define('PROJECTS_PER_PAGE', 12);

// Currency
define('CURRENCY_SYMBOL', '₹');
define('CURRENCY_CODE', 'INR');

// Package Pricing
define('BUSINESS_PREMIUM_PRICE', 15000);
define('ECOMMERCE_PACKAGE_PRICE', 75000);

// Email Limits
define('FREE_EMAILS_PER_MONTH', 500);

// Time Zone
date_default_timezone_set('Asia/Kolkata');

// Error Reporting (Set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
} catch (PDOException $e) {
    // Log error and show user-friendly message
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please try again later.");
}

// Start output buffering
ob_start();

// Security Headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// CSRF Token Generation
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Helper function to generate CSRF token input
function csrf_token_input() {
    return '<input type="hidden" name="csrf_token" value="' . $_SESSION['csrf_token'] . '">';
}

// Helper function to verify CSRF token
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Helper function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Helper function to validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Helper function to validate phone number (Indian format)
function validate_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^[6-9]\d{9}$/', $phone);
}

// Helper function to generate random string
function generate_random_string($length = 10) {
    return bin2hex(random_bytes($length / 2));
}

// Helper function to format currency
function format_currency($amount) {
    return CURRENCY_SYMBOL . number_format($amount, 0, '.', ',');
}

// Helper function to format date
function format_date($date, $format = 'd M Y') {
    return date($format, strtotime($date));
}

// Helper function to get time ago
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'अभी';
    if ($time < 3600) return floor($time/60) . ' मिनट पहले';
    if ($time < 86400) return floor($time/3600) . ' घंटे पहले';
    if ($time < 2592000) return floor($time/86400) . ' दिन पहले';
    if ($time < 31536000) return floor($time/2592000) . ' महीने पहले';
    return floor($time/31536000) . ' साल पहले';
}

// Helper function to truncate text
function truncate_text($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

// Helper function to create slug
function create_slug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

// Helper function to check if user is logged in
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Helper function to check user role
function has_role($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

// Helper function to redirect
function redirect($url) {
    header("Location: $url");
    exit();
}

// Helper function to show alert message
function set_alert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

// Helper function to display alert
function show_alert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        echo '<div class="alert alert-' . $alert['type'] . ' alert-dismissible fade show" role="alert">';
        echo $alert['message'];
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['alert']);
    }
}

// Helper function to log activity
function log_activity($user_id, $action, $details = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([$user_id, $action, $details, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']]);
    } catch (PDOException $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

// Helper function to send email
function send_email($to, $subject, $body, $from_name = SITE_NAME, $from_email = SITE_EMAIL) {
    // This will be implemented with PHPMailer
    // For now, using basic mail function
    $headers = "From: $from_name <$from_email>\r\n";
    $headers .= "Reply-To: $from_email\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $body, $headers);
}
?>
