<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

// Log the logout activity if user is logged in
if (is_logged_in()) {
    log_activity($_SESSION['user_id'], 'logout', 'User logged out');
}

// Clear remember me cookie if exists
if (isset($_CO<PERSON>IE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
    
    // Remove token from database if you implement remember me functionality
    // $stmt = $pdo->prepare("DELETE FROM remember_tokens WHERE token = ?");
    // $stmt->execute([hash('sha256', $_<PERSON><PERSON>IE['remember_token'])]);
}

// Destroy session
session_destroy();

// Redirect to home page with logout message
redirect('index.php?logout=1');
?>
