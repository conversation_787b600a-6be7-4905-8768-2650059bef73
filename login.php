<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

// Redirect if already logged in
if (is_logged_in()) {
    redirect('dashboard/');
}

$page_title = "लॉगिन - " . SITE_NAME;
$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'अमान्य अनुरोध। कृपया पुनः प्रयास करें।';
    } else {
        $email = sanitize_input($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember_me = isset($_POST['remember_me']);
        
        if (empty($email) || empty($password)) {
            $error_message = 'कृपया सभी फील्ड भरें';
        } else {
            $result = login_user($email, $password);
            
            if ($result['success']) {
                // Set remember me cookie if requested
                if ($remember_me) {
                    $token = generate_random_string(32);
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true); // 30 days
                    
                    // Store token in database (you'd need to add this table)
                    // $stmt = $pdo->prepare("INSERT INTO remember_tokens (user_id, token, expires_at) VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY))");
                    // $stmt->execute([$_SESSION['user_id'], hash('sha256', $token)]);
                }
                
                // Redirect based on user role
                $redirect_url = 'dashboard/';
                if (has_role('admin')) {
                    $redirect_url = 'dashboard/admin/';
                } elseif (has_role('vendor')) {
                    $redirect_url = 'dashboard/vendor/';
                }
                
                redirect($redirect_url);
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

// Check for success message from registration
if (isset($_GET['registered'])) {
    $success_message = 'पंजीकरण सफल! कृपया लॉगिन करें।';
}

if (isset($_GET['verified'])) {
    $success_message = 'ईमेल वेरिफिकेशन सफल! अब आप लॉगिन कर सकते हैं।';
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo SITE_NAME; ?> में लॉगिन करें और अपने प्रोजेक्ट्स को मैनेज करें।">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Login Section -->
    <section class="auth-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="auth-card">
                        <div class="auth-header text-center">
                            <h2>लॉगिन करें</h2>
                            <p>अपने अकाउंट में वापस आएं</p>
                        </div>
                        
                        <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="auth-form">
                            <?php echo csrf_token_input(); ?>
                            
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">ईमेल एड्रेस</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           placeholder="आपका ईमेल एड्रेस" required>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="password" class="form-label">पासवर्ड</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="आपका पासवर्ड" required>
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        मुझे याद रखें
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group mb-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt"></i> लॉगिन करें
                                </button>
                            </div>
                            
                            <div class="auth-links text-center">
                                <p><a href="forgot-password.php">पासवर्ड भूल गए?</a></p>
                                <p>अकाउंट नहीं है? <a href="register.php">यहाँ रजिस्टर करें</a></p>
                            </div>
                        </form>
                        
                        <div class="auth-divider">
                            <span>या</span>
                        </div>
                        
                        <div class="social-login">
                            <button type="button" class="btn btn-outline-danger w-100 mb-2">
                                <i class="fab fa-google"></i> Google से लॉगिन करें
                            </button>
                            <button type="button" class="btn btn-outline-primary w-100">
                                <i class="fab fa-facebook-f"></i> Facebook से लॉगिन करें
                            </button>
                        </div>
                    </div>
                    
                    <!-- Quick Access for Demo -->
                    <div class="demo-credentials mt-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">डेमो अकाउंट्स</h6>
                            </div>
                            <div class="card-body">
                                <small class="text-muted">
                                    <strong>Admin:</strong> <EMAIL> / admin123<br>
                                    <strong>Client:</strong> <EMAIL> / client123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Auto-fill demo credentials
    document.addEventListener('DOMContentLoaded', function() {
        const demoButtons = document.querySelectorAll('.demo-credentials small');
        if (demoButtons.length > 0) {
            demoButtons[0].addEventListener('click', function() {
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = 'admin123';
            });
        }
    });
    </script>
</body>
</html>
