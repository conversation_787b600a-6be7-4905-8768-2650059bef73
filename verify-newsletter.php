<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

$page_title = "न्यूज़लेटर वेरिफिकेशन - " . SITE_NAME;
$message = '';
$message_type = 'info';

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = sanitize_input($_GET['token']);
    
    try {
        // Find subscriber with this token
        $stmt = $pdo->prepare("SELECT id, email, status FROM newsletter_subscribers WHERE verification_token = ? AND status = 'pending'");
        $stmt->execute([$token]);
        $subscriber = $stmt->fetch();
        
        if ($subscriber) {
            // Update subscriber status to active
            $stmt = $pdo->prepare("UPDATE newsletter_subscribers SET status = 'active', verified_at = NOW(), verification_token = NULL WHERE id = ?");
            $stmt->execute([$subscriber['id']]);
            
            $message = 'बधाई हो! आपका न्यूज़लेटर सब्सक्रिप्शन सफलतापूर्वक वेरिफाई हो गया है।';
            $message_type = 'success';
            
            // Log the verification
            error_log("Newsletter verified for: " . $subscriber['email']);
            
        } else {
            $message = 'अमान्य या expired वेरिफिकेशन लिंक। कृपया दोबारा सब्सक्राइब करें।';
            $message_type = 'danger';
        }
        
    } catch (PDOException $e) {
        error_log("Newsletter verification error: " . $e->getMessage());
        $message = 'वेरिफिकेशन में त्रुटि हुई। कृपया बाद में पुनः प्रयास करें।';
        $message_type = 'danger';
    }
    
} else {
    $message = 'वेरिफिकेशन टोकन नहीं मिला। कृपया अपना ईमेल चेक करें।';
    $message_type = 'warning';
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Verification Section -->
    <section class="verification-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="verification-card">
                        <div class="verification-header text-center">
                            <?php if ($message_type === 'success'): ?>
                            <div class="verification-icon success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h2>वेरिफिकेशन सफल!</h2>
                            <?php elseif ($message_type === 'danger'): ?>
                            <div class="verification-icon error">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <h2>वेरिफिकेशन असफल</h2>
                            <?php else: ?>
                            <div class="verification-icon warning">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <h2>वेरिफिकेशन आवश्यक</h2>
                            <?php endif; ?>
                        </div>
                        
                        <div class="verification-content">
                            <div class="alert alert-<?php echo $message_type; ?>">
                                <?php echo $message; ?>
                            </div>
                            
                            <?php if ($message_type === 'success'): ?>
                            <div class="success-info">
                                <h5>अब आप प्राप्त करेंगे:</h5>
                                <ul>
                                    <li><i class="fas fa-check"></i> वेब डेवलपमेंट टिप्स और ट्रिक्स</li>
                                    <li><i class="fas fa-check"></i> नई सेवाओं की जानकारी</li>
                                    <li><i class="fas fa-check"></i> विशेष छूट और ऑफर्स</li>
                                    <li><i class="fas fa-check"></i> Industry updates और trends</li>
                                </ul>
                                
                                <div class="newsletter-preferences">
                                    <h6>न्यूज़लेटर प्राथमिकताएं:</h6>
                                    <p>आप किसी भी समय अपनी preferences update कर सकते हैं या unsubscribe कर सकते हैं।</p>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="verification-actions text-center">
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-home"></i> होम पेज पर जाएं
                                </a>
                                
                                <?php if ($message_type !== 'success'): ?>
                                <a href="#newsletter-signup" class="btn btn-outline-primary" data-bs-toggle="modal">
                                    <i class="fas fa-envelope"></i> दोबारा सब्सक्राइब करें
                                </a>
                                <?php else: ?>
                                <a href="services.php" class="btn btn-outline-primary">
                                    <i class="fas fa-cogs"></i> हमारी सेवाएं देखें
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Newsletter Signup Modal -->
    <div class="modal fade" id="newsletter-signup" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">न्यूज़लेटर सब्सक्रिप्शन</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="newsletterForm">
                        <div class="mb-3">
                            <label for="newsletter_email" class="form-label">ईमेल एड्रेस</label>
                            <input type="email" class="form-control" id="newsletter_email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="newsletter_name" class="form-label">नाम (वैकल्पिक)</label>
                            <input type="text" class="form-control" id="newsletter_name" name="name">
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="newsletter_terms" required>
                            <label class="form-check-label" for="newsletter_terms">
                                मैं <a href="legal/privacy-policy.php" target="_blank">प्राइवेसी पॉलिसी</a> से सहमत हूं
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i> सब्सक्राइब करें
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
    // Newsletter form submission
    document.getElementById('newsletterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> भेजा जा रहा है...';
        submitBtn.disabled = true;
        
        try {
            const response = await fetch('api/newsletter-subscribe.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert('सब्सक्रिप्शन सफल! कृपया अपना ईमेल चेक करें।');
                this.reset();
                bootstrap.Modal.getInstance(document.getElementById('newsletter-signup')).hide();
            } else {
                alert(result.message || 'सब्सक्रिप्शन में त्रुटि हुई।');
            }
        } catch (error) {
            alert('सब्सक्रिप्शन में त्रुटि हुई। कृपया पुनः प्रयास करें।');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
    </script>
    
    <style>
    .verification-section {
        padding: 100px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 80vh;
        display: flex;
        align-items: center;
    }
    
    .verification-card {
        background: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 40px;
        text-align: center;
    }
    
    .verification-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: var(--white-color);
    }
    
    .verification-icon.success {
        background: var(--success-color);
    }
    
    .verification-icon.error {
        background: var(--danger-color);
    }
    
    .verification-icon.warning {
        background: var(--warning-color);
    }
    
    .verification-header h2 {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: var(--dark-color);
    }
    
    .verification-content {
        margin-top: 30px;
    }
    
    .success-info {
        text-align: left;
        margin: 30px 0;
    }
    
    .success-info ul {
        list-style: none;
        padding: 0;
    }
    
    .success-info li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .success-info li:last-child {
        border-bottom: none;
    }
    
    .success-info i {
        color: var(--success-color);
        margin-right: 10px;
        width: 16px;
    }
    
    .newsletter-preferences {
        background: var(--light-color);
        padding: 20px;
        border-radius: var(--border-radius);
        margin-top: 20px;
    }
    
    .newsletter-preferences h6 {
        color: var(--dark-color);
        margin-bottom: 10px;
    }
    
    .newsletter-preferences p {
        color: var(--secondary-color);
        margin-bottom: 0;
        font-size: 14px;
    }
    
    .verification-actions {
        margin-top: 30px;
    }
    
    .verification-actions .btn {
        margin: 5px 10px;
    }
    
    @media (max-width: 767.98px) {
        .verification-section {
            padding: 60px 0;
        }
        
        .verification-card {
            padding: 30px 20px;
        }
        
        .verification-icon {
            width: 60px;
            height: 60px;
            font-size: 2rem;
        }
        
        .verification-header h2 {
            font-size: 1.5rem;
        }
        
        .verification-actions .btn {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
    </style>
</body>
</html>
