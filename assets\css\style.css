/* WebsiteDeveloper0002 - Main Stylesheet */

/* CSS Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white-color: #ffffff;
    --black-color: #000000;
    
    --primary-gradient: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    --secondary-gradient: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    
    --font-family-primary: 'Poppins', sans-serif;
    --font-family-secondary: 'Roboto', sans-serif;
    
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: #0056b3;
    text-decoration: none;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white-color);
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 123, 255, 0.3);
    color: var(--white-color);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

.btn-lg {
    padding: 16px 32px;
    font-size: 18px;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

/* Header Styles */
.main-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--white-color);
}

.top-bar {
    background: var(--dark-color);
    color: var(--white-color);
    padding: 8px 0;
    font-size: 14px;
}

.top-bar .contact-info span {
    margin-right: 20px;
}

.top-bar .contact-info i {
    margin-right: 5px;
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: var(--primary-color);
    color: var(--white-color);
    border-radius: 50%;
    margin-left: 8px;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--white-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.navbar {
    padding: 15px 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-color);
}

.navbar-brand img {
    margin-right: 10px;
}

.brand-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 10px 15px;
    color: var(--dark-color);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/hero-pattern.png') repeat;
    opacity: 0.1;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.hero-title .text-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 30px;
    color: var(--secondary-color);
}

.hero-features {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
}

.feature-item i {
    color: var(--success-color);
    margin-right: 10px;
    font-size: 18px;
}

.hero-buttons .btn {
    margin-right: 15px;
    margin-bottom: 15px;
}

.hero-image {
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
}

/* Services Overview */
.services-overview {
    padding: 80px 0;
    background: var(--white-color);
}

.section-header {
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--secondary-color);
}

.service-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 40px 30px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: 1px solid #e9ecef;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.service-card.featured {
    border: 2px solid var(--primary-color);
    position: relative;
}

.service-card.featured::before {
    content: 'सबसे लोकप्रिय';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-gradient);
    color: var(--white-color);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 32px;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.service-card .price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.service-card .price span {
    font-size: 14px;
    color: var(--secondary-color);
    font-weight: 400;
}

.service-features {
    list-style: none;
    margin-bottom: 30px;
    text-align: left;
}

.service-features li {
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.service-features li:last-child {
    border-bottom: none;
}

.service-features i {
    color: var(--success-color);
    margin-right: 10px;
    width: 16px;
}

.service-buttons .btn {
    margin: 5px;
    width: calc(50% - 10px);
}

/* Why Choose Us */
.why-choose-us {
    padding: 80px 0;
    background: var(--light-color);
}

.feature-box {
    padding: 40px 20px;
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 28px;
}

.feature-box h4 {
    font-size: 1.25rem;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.feature-box p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: var(--white-color);
}

.contact-form {
    background: var(--light-color);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 16px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #25d366;
    color: var(--white-color);
    border-radius: 50%;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-float a:hover {
    transform: scale(1.1);
    color: var(--white-color);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
    100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
}

/* Breadcrumb */
.breadcrumb-section {
    background: var(--light-color);
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    background: transparent;
    margin-bottom: 0;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--secondary-color);
}

.breadcrumb-item a {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--secondary-color);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-light { color: var(--light-color) !important; }
.text-dark { color: var(--dark-color) !important; }
.text-white { color: var(--white-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }
.bg-white { background-color: var(--white-color) !important; }

.shadow { box-shadow: var(--box-shadow) !important; }
.shadow-sm { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important; }
.shadow-lg { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-circle { border-radius: 50% !important; }

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

.animate-fadeIn { animation: fadeIn 0.6s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.6s ease-out; }
.animate-slideInRight { animation: slideInRight 0.6s ease-out; }

/* Footer Styles */
.main-footer {
    background: var(--dark-color);
    color: var(--white-color);
    margin-top: 80px;
}

.newsletter-section {
    background: var(--primary-gradient);
    padding: 60px 0;
    color: var(--white-color);
}

.newsletter-content h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--white-color);
}

.newsletter-content p {
    font-size: 1.1rem;
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.9);
}

.newsletter-form {
    max-width: 400px;
    margin-left: auto;
}

.newsletter-form .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.newsletter-form .form-control {
    border: none;
    padding: 15px 20px;
    font-size: 16px;
}

.newsletter-form .btn {
    border: none;
    padding: 15px 25px;
    background: var(--white-color);
    color: var(--primary-color);
    font-weight: 600;
}

.newsletter-form .btn:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.footer-main {
    padding: 60px 0 40px;
}

.footer-widget {
    margin-bottom: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    margin-right: 10px;
}

.footer-logo .brand-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--white-color);
    background: none;
    -webkit-text-fill-color: var(--white-color);
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 25px;
    line-height: 1.7;
}

.footer-contact p {
    margin-bottom: 10px;
    color: rgba(255, 255, 255, 0.9);
}

.footer-contact i {
    color: var(--primary-color);
    margin-right: 10px;
    width: 16px;
}

.footer-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--white-color);
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--primary-color);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    display: block;
    padding: 5px 0;
}

.footer-links a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-bottom-info {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.social-media h6 {
    color: var(--white-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.footer-bottom-info .social-links {
    display: flex;
    gap: 10px;
}

.footer-bottom-info .social-links a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white-color);
    border-radius: 50%;
    transition: var(--transition);
}

.footer-bottom-info .social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.payment-methods h6 {
    color: var(--white-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.payment-icons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.payment-icons img {
    height: 30px;
    width: auto;
    background: var(--white-color);
    padding: 5px;
    border-radius: 4px;
    transition: var(--transition);
}

.payment-icons img:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.footer-copyright {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright-text {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.8);
}

.footer-credits {
    margin-bottom: 0;
}

.footer-credits p {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.8);
}

.footer-credits a {
    color: var(--primary-color);
    font-weight: 600;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: var(--white-color);
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 999;
    box-shadow: var(--box-shadow);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.4);
}

/* Cookie Consent */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--dark-color);
    color: var(--white-color);
    padding: 20px 0;
    z-index: 1001;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
}

.cookie-text {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.9);
}

.cookie-text a {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.loading-text {
    color: var(--white-color);
    margin-top: 20px;
    font-size: 16px;
}

/* Legal Pages Styles */
.legal-content {
    padding: 80px 0;
    background: var(--white-color);
}

.legal-document {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 40px;
    margin-bottom: 30px;
}

.legal-document h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 15px;
}

.last-updated {
    color: var(--secondary-color);
    font-style: italic;
    margin-bottom: 30px;
    font-size: 14px;
}

.legal-section {
    margin-bottom: 40px;
}

.legal-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
    border-left: 4px solid var(--primary-color);
    padding-left: 15px;
}

.legal-section h3 {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 10px;
    margin-top: 20px;
}

.legal-section p {
    line-height: 1.8;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.legal-section ul,
.legal-section ol {
    margin-bottom: 20px;
    padding-left: 25px;
}

.legal-section li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: var(--dark-color);
}

.contact-info {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 20px;
}

.contact-info p {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.contact-info strong {
    color: var(--primary-color);
}

.legal-footer {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
    margin-top: 40px;
    text-align: center;
}

.legal-footer p {
    color: var(--secondary-color);
    font-size: 14px;
    margin-bottom: 0;
}

/* Auth Pages Styles */
.auth-page {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    min-height: 100vh;
}

.auth-section {
    padding: 80px 0;
}

.auth-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 40px;
    margin-bottom: 30px;
}

.auth-header {
    margin-bottom: 30px;
}

.auth-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.auth-header p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form .form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.auth-form .input-group-text {
    background: var(--light-color);
    border-color: #e9ecef;
    color: var(--primary-color);
}

.auth-form .form-control {
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-size: 16px;
}

.auth-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.auth-links {
    margin-top: 20px;
}

.auth-links a {
    color: var(--primary-color);
    font-weight: 500;
}

.auth-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: var(--white-color);
    padding: 0 20px;
    color: var(--secondary-color);
    font-size: 14px;
}

.social-login .btn {
    margin-bottom: 10px;
}

.demo-credentials .card {
    border: 1px solid #e9ecef;
}

.demo-credentials .card-header {
    background: var(--light-color);
    border-bottom: 1px solid #e9ecef;
    padding: 10px 15px;
}

.demo-credentials .card-body {
    padding: 15px;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 10px;
}

.strength-meter {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-meter-fill {
    height: 100%;
    width: 0%;
    transition: var(--transition);
    border-radius: 2px;
}

.strength-text {
    font-size: 12px;
    font-weight: 500;
}
