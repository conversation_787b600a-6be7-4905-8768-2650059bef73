# WebsiteDeveloper0002 - Robots.txt
# Website: https://websitedeveloper0002.in

# Allow all search engines to crawl the website
User-agent: *

# Allow crawling of main pages
Allow: /
Allow: /services
Allow: /portfolio
Allow: /about
Allow: /contact
Allow: /blog/
Allow: /legal/

# Allow crawling of assets
Allow: /assets/css/
Allow: /assets/js/
Allow: /assets/images/

# Disallow sensitive directories and files
Disallow: /includes/
Disallow: /database/
Disallow: /api/
Disallow: /dashboard/
Disallow: /uploads/private/
Disallow: /logs/
Disallow: /backups/
Disallow: /vendor/
Disallow: /config/
Disallow: /.git/
Disallow: /.htaccess
Disallow: /error-pages/

# Disallow authentication and admin pages
Disallow: /login
Disallow: /register
Disallow: /logout
Disallow: /forgot-password
Disallow: /reset-password
Disallow: /verify-email
Disallow: /admin/
Disallow: /dashboard/

# Disallow search and filter pages to prevent duplicate content
Disallow: /*?search=
Disallow: /*?filter=
Disallow: /*?sort=
Disallow: /*?page=
Disallow: /*?utm_
Disallow: /*?ref=
Disallow: /*?source=

# Disallow file types that shouldn't be indexed
Disallow: /*.sql$
Disallow: /*.log$
Disallow: /*.bak$
Disallow: /*.backup$
Disallow: /*.tmp$
Disallow: /*.conf$
Disallow: /*.ini$

# Block specific bots (optional - uncomment if needed)
# User-agent: AhrefsBot
# Disallow: /

# User-agent: MJ12bot
# Disallow: /

# User-agent: SemrushBot
# Disallow: /

# User-agent: DotBot
# Disallow: /

# Allow Google and Bing specifically (redundant but explicit)
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# Crawl delay for all bots (1 second)
Crawl-delay: 1

# Sitemap location
Sitemap: https://websitedeveloper0002.in/sitemap.xml

# Additional sitemaps (if you create them)
# Sitemap: https://websitedeveloper0002.in/blog-sitemap.xml
# Sitemap: https://websitedeveloper0002.in/portfolio-sitemap.xml
