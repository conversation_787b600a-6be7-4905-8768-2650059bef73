<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        // Fallback to POST data
        $email = sanitize_input($_POST['email'] ?? '');
        $name = sanitize_input($_POST['name'] ?? '');
    } else {
        $email = sanitize_input($input['email'] ?? '');
        $name = sanitize_input($input['name'] ?? '');
    }
    
    // Validation
    if (empty($email)) {
        echo json_encode([
            'success' => false,
            'message' => 'ईमेल एड्रेस आवश्यक है'
        ]);
        exit;
    }
    
    if (!validate_email($email)) {
        echo json_encode([
            'success' => false,
            'message' => 'वैध ईमेल एड्रेस दर्ज करें'
        ]);
        exit;
    }
    
    // Check if email already exists
    $stmt = $pdo->prepare("SELECT id, status FROM newsletter_subscribers WHERE email = ?");
    $stmt->execute([$email]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        if ($existing['status'] === 'active') {
            echo json_encode([
                'success' => false,
                'message' => 'यह ईमेल पहले से सब्सक्राइब है'
            ]);
            exit;
        } else {
            // Reactivate subscription
            $stmt = $pdo->prepare("UPDATE newsletter_subscribers SET status = 'active', subscribed_at = NOW(), unsubscribed_at = NULL WHERE email = ?");
            $stmt->execute([$email]);
            
            echo json_encode([
                'success' => true,
                'message' => 'न्यूज़लेटर सब्सक्रिप्शन पुनः सक्रिय कर दिया गया'
            ]);
            exit;
        }
    }
    
    // Generate verification token
    $verification_token = generate_random_string(32);
    
    // Insert new subscriber
    $stmt = $pdo->prepare("INSERT INTO newsletter_subscribers (email, name, verification_token, source) VALUES (?, ?, ?, 'website')");
    $stmt->execute([$email, $name, $verification_token]);
    
    // Send verification email
    $verification_link = SITE_URL . "/verify-newsletter.php?token=" . $verification_token;
    
    $subject = "न्यूज़लेटर सब्सक्रिप्शन कन्फर्म करें - " . SITE_NAME;
    $body = "
    <h2>न्यूज़लेटर सब्सक्रिप्शन</h2>
    <p>नमस्ते" . ($name ? " {$name}" : "") . ",</p>
    <p>" . SITE_NAME . " के न्यूज़लेटर के लिए धन्यवाद!</p>
    <p>कृपया अपना सब्सक्रिप्शन कन्फर्म करने के लिए नीचे दिए गए लिंक पर क्लिक करें:</p>
    <p><a href='{$verification_link}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>सब्सक्रिप्शन कन्फर्म करें</a></p>
    <p>यदि आपने यह अनुरोध नहीं किया है, तो कृपया इस ईमेल को अनदेखा करें।</p>
    <p>धन्यवाद,<br>" . SITE_NAME . " टीम</p>
    ";
    
    if (send_email($email, $subject, $body)) {
        echo json_encode([
            'success' => true,
            'message' => 'सब्सक्रिप्शन सफल! कृपया अपना ईमेल चेक करें और कन्फर्म करें।'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'सब्सक्रिप्शन सफल!'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Newsletter subscription error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'सब्सक्रिप्शन में त्रुटि हुई। कृपया पुनः प्रयास करें।'
    ]);
}
?>
