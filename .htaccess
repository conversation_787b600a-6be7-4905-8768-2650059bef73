# WebsiteDeveloper0002 - Apache Configuration
# Security and Performance Optimizations

# Enable Rewrite Engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Strict Transport Security (HTTPS only)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://www.googletagmanager.com https://checkout.razorpay.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com https://api.razorpay.com; frame-src https://api.razorpay.com https://www.google.com;"
    
    # Remove Server Information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide Server Information
ServerTokens Prod
ServerSignature Off

# Redirect HTTP to HTTPS (Uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove www (or add www - choose one)
# Remove www
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Add www (alternative - comment out the above if using this)
# RewriteCond %{HTTP_HOST} !^www\. [NC]
# RewriteRule ^(.*)$ https://www.%{HTTP_HOST}/$1 [R=301,L]

# Pretty URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^.]+)$ $1.php [L]

# Redirect .php extension to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1? [NC,L,R=301]

# Custom Error Pages
ErrorDocument 400 /error-pages/400.php
ErrorDocument 401 /error-pages/401.php
ErrorDocument 403 /error-pages/403.php
ErrorDocument 404 /error-pages/404.php
ErrorDocument 500 /error-pages/500.php
ErrorDocument 502 /error-pages/502.php
ErrorDocument 503 /error-pages/503.php

# File Protection
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "functions.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Protect sensitive directories
<IfModule mod_rewrite.c>
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^database/ - [F,L]
    RewriteRule ^logs/ - [F,L]
    RewriteRule ^backups/ - [F,L]
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
    
    # Remove browser bugs (only needed for really old browsers)
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # Documents
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/vnd.ms-powerpoint "access plus 1 month"
    ExpiresByType application/vnd.ms-excel "access plus 1 month"
    ExpiresByType application/msword "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # Default
    ExpiresDefault "access plus 1 week"
</IfModule>

# Cache Control Headers
<IfModule mod_headers.c>
    # Cache static assets for 1 month
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|otf)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
    
    # Cache HTML for 1 hour
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "max-age=3600, public, must-revalidate"
    </FilesMatch>
</IfModule>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?websitedeveloper0002\.in [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?google\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?bing\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yahoo\. [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [F]

# Limit file upload size (adjust as needed)
LimitRequestBody 10485760

# Disable directory browsing
Options -Indexes

# Follow symbolic links
Options +FollowSymLinks

# Prevent access to version control directories
RedirectMatch 404 /\.git

# Block bad bots and scrapers
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} (ahrefs|alexibot|majestic|mj12bot|rogerbot) [NC]
    RewriteRule .* - [F]
</IfModule>

# Rate limiting (basic)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# PHP Configuration (if allowed)
<IfModule mod_php.c>
    # Hide PHP version
    php_flag expose_php Off
    
    # Increase security
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    
    # Session security
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_only_cookies 1
    
    # Upload limits
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
</IfModule>

# Maintenance Mode (uncomment to enable)
# RewriteCond %{REQUEST_URI} !/maintenance.html$
# RewriteCond %{REMOTE_ADDR} !^123\.456\.789\.000
# RewriteRule $ /maintenance.html [R=302,L]
