<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>साइट मेंटेनेंस - WebsiteDeveloper0002</title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .maintenance-container {
            text-align: center;
            background: white;
            padding: 60px 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }
        
        .maintenance-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 30px;
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
        }
        
        .maintenance-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .maintenance-message {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }
        
        .maintenance-message p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .maintenance-message p:last-child {
            margin-bottom: 0;
        }
        
        .contact-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .contact-info h4 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }
        
        .contact-item i {
            margin-right: 10px;
            color: #1976d2;
            width: 20px;
        }
        
        .contact-item a {
            color: #1976d2;
            text-decoration: none;
        }
        
        .contact-item a:hover {
            text-decoration: underline;
        }
        
        .progress-bar {
            background: #e0e0e0;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            width: 0%;
            border-radius: 4px;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 0%; }
        }
        
        .social-links {
            margin-top: 30px;
        }
        
        .social-links a {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-decoration: none;
            margin: 0 10px;
            line-height: 50px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .social-links a:hover {
            background: #764ba2;
            transform: translateY(-3px);
        }
        
        .countdown {
            background: #fff3e0;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid #ff9800;
        }
        
        .countdown h4 {
            color: #f57c00;
            margin-bottom: 15px;
        }
        
        .countdown-timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #e65100;
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 40px 20px;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-subtitle {
                font-size: 1rem;
            }
            
            .contact-item {
                flex-direction: column;
                text-align: center;
            }
            
            .contact-item i {
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <h1 class="maintenance-title">साइट मेंटेनेंस</h1>
        <p class="maintenance-subtitle">हम अपनी वेबसाइट को बेहतर बनाने के लिए काम कर रहे हैं</p>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="maintenance-message">
            <p><strong>क्षमा करें!</strong> हमारी वेबसाइट अभी maintenance mode में है।</p>
            <p>हम अपनी सेवाओं को बेहतर बनाने और नई features जोड़ने के लिए काम कर रहे हैं।</p>
            <p>जल्द ही हम वापस आएंगे। धन्यवाद आपके धैर्य के लिए!</p>
        </div>
        
        <div class="countdown">
            <h4><i class="fas fa-clock"></i> अनुमानित समय</h4>
            <div class="countdown-timer" id="countdown">
                लगभग 2 घंटे
            </div>
        </div>
        
        <div class="contact-info">
            <h4><i class="fas fa-headset"></i> आपातकालीन संपर्क</h4>
            <div class="contact-item">
                <i class="fas fa-phone"></i>
                <a href="tel:+919876543210">+91 98765 43210</a>
            </div>
            <div class="contact-item">
                <i class="fab fa-whatsapp"></i>
                <a href="https://wa.me/919876543210" target="_blank">WhatsApp पर संदेश भेजें</a>
            </div>
            <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
        </div>
        
        <div class="spinner"></div>
        
        <div class="social-links">
            <a href="#" target="_blank" title="Facebook">
                <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" target="_blank" title="Twitter">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" target="_blank" title="Instagram">
                <i class="fab fa-instagram"></i>
            </a>
            <a href="#" target="_blank" title="LinkedIn">
                <i class="fab fa-linkedin-in"></i>
            </a>
        </div>
        
        <p style="margin-top: 30px; color: #999; font-size: 14px;">
            © 2025 WebsiteDeveloper0002. सभी अधिकार सुरक्षित।
        </p>
    </div>
    
    <script>
        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes
        
        // Simple countdown (for demo purposes)
        let timeLeft = 7200; // 2 hours in seconds
        
        function updateCountdown() {
            const hours = Math.floor(timeLeft / 3600);
            const minutes = Math.floor((timeLeft % 3600) / 60);
            
            if (timeLeft > 0) {
                document.getElementById('countdown').textContent = 
                    `${hours} घंटे ${minutes} मिनट`;
                timeLeft--;
            } else {
                document.getElementById('countdown').textContent = 'जल्द ही...';
            }
        }
        
        // Update countdown every minute
        setInterval(updateCountdown, 60000);
        updateCountdown();
    </script>
</body>
</html>
