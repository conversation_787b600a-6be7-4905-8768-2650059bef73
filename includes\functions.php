<?php
// Utility Functions

// Check if user is logged in
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Check user role
function has_role($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

// Redirect function
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// Authentication Functions

function login_user($email, $password) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id, username, email, password_hash, first_name, last_name, role, status, login_attempts, locked_until FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'अमान्य ईमेल या पासवर्ड'];
        }
        
        // Check if account is locked
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            return ['success' => false, 'message' => 'अकाउंट लॉक है। कुछ समय बाद कोशिश करें।'];
        }
        
        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            // Increment login attempts
            $attempts = $user['login_attempts'] + 1;
            $locked_until = null;
            
            if ($attempts >= MAX_LOGIN_ATTEMPTS) {
                $locked_until = date('Y-m-d H:i:s', time() + LOGIN_LOCKOUT_TIME);
            }
            
            $stmt = $pdo->prepare("UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?");
            $stmt->execute([$attempts, $locked_until, $user['id']]);
            
            return ['success' => false, 'message' => 'अमान्य ईमेल या पासवर्ड'];
        }
        
        // Reset login attempts and update last login
        $stmt = $pdo->prepare("UPDATE users SET login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['login_time'] = time();
        
        // Log activity
        log_activity($user['id'], 'login', 'User logged in successfully');
        
        return ['success' => true, 'message' => 'सफलतापूर्वक लॉगिन हो गए', 'user' => $user];
        
    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'लॉगिन में त्रुटि हुई'];
    }
}

function register_user($data) {
    global $pdo;
    
    try {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$data['email']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'यह ईमेल पहले से पंजीकृत है'];
        }
        
        // Check if username already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$data['username']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'यह यूजरनेम पहले से उपयोग में है'];
        }
        
        // Hash password
        $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // Generate verification token
        $verification_token = generate_random_string(32);
        
        // Insert user
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash, first_name, last_name, phone, role, email_verification_token) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $data['username'],
            $data['email'],
            $password_hash,
            $data['first_name'],
            $data['last_name'],
            $data['phone'] ?? null,
            $data['role'] ?? 'client',
            $verification_token
        ]);
        
        $user_id = $pdo->lastInsertId();
        
        // Send verification email
        send_verification_email($data['email'], $data['first_name'], $verification_token);
        
        // Log activity
        log_activity($user_id, 'register', 'User registered successfully');
        
        return ['success' => true, 'message' => 'पंजीकरण सफल! कृपया अपना ईमेल वेरिफाई करें।'];
        
    } catch (PDOException $e) {
        error_log("Registration error: " . $e->getMessage());
        return ['success' => false, 'message' => 'पंजीकरण में त्रुटि हुई'];
    }
}

function logout_user() {
    if (isset($_SESSION['user_id'])) {
        log_activity($_SESSION['user_id'], 'logout', 'User logged out');
    }
    
    session_destroy();
    redirect('index.php');
}

// Project Management Functions

function create_project($data) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO projects (client_id, title, description, package_type, price, requirements, status) VALUES (?, ?, ?, ?, ?, ?, 'pending')");
        $stmt->execute([
            $data['client_id'],
            $data['title'],
            $data['description'],
            $data['package_type'],
            $data['price'],
            $data['requirements'] ?? ''
        ]);
        
        $project_id = $pdo->lastInsertId();
        
        // Log activity
        log_activity($data['client_id'], 'project_created', "Project created: {$data['title']}");
        
        return ['success' => true, 'project_id' => $project_id];
        
    } catch (PDOException $e) {
        error_log("Project creation error: " . $e->getMessage());
        return ['success' => false, 'message' => 'प्रोजेक्ट बनाने में त्रुटि हुई'];
    }
}

function get_user_projects($user_id, $limit = null) {
    global $pdo;
    
    try {
        $sql = "SELECT p.*, u.first_name, u.last_name, u.email FROM projects p 
                JOIN users u ON p.client_id = u.id 
                WHERE p.client_id = ? 
                ORDER BY p.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user_id]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get user projects error: " . $e->getMessage());
        return [];
    }
}

// Order Management Functions

function create_order($data) {
    global $pdo;
    
    try {
        $order_number = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        $stmt = $pdo->prepare("INSERT INTO orders (client_id, project_id, order_number, amount, tax_amount, total_amount, currency) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $data['client_id'],
            $data['project_id'] ?? null,
            $order_number,
            $data['amount'],
            $data['tax_amount'] ?? 0,
            $data['total_amount'],
            $data['currency'] ?? 'INR'
        ]);
        
        $order_id = $pdo->lastInsertId();
        
        // Log activity
        log_activity($data['client_id'], 'order_created', "Order created: {$order_number}");
        
        return ['success' => true, 'order_id' => $order_id, 'order_number' => $order_number];
        
    } catch (PDOException $e) {
        error_log("Order creation error: " . $e->getMessage());
        return ['success' => false, 'message' => 'ऑर्डर बनाने में त्रुटि हुई'];
    }
}

// Contact Form Functions

function save_contact_inquiry($data) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO contact_inquiries (name, email, phone, service, message, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $data['name'],
            $data['email'],
            $data['phone'] ?? null,
            $data['service'] ?? null,
            $data['message'],
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
        
        $inquiry_id = $pdo->lastInsertId();
        
        // Send thank you email
        send_contact_thank_you_email($data['email'], $data['name']);
        
        // Send notification to admin
        send_contact_notification_email($data);
        
        return ['success' => true, 'inquiry_id' => $inquiry_id];
        
    } catch (PDOException $e) {
        error_log("Contact inquiry error: " . $e->getMessage());
        return ['success' => false, 'message' => 'संदेश भेजने में त्रुटि हुई'];
    }
}

// Email Functions

function send_verification_email($email, $name, $token) {
    $subject = "ईमेल वेरिफिकेशन - " . SITE_NAME;
    $verification_link = SITE_URL . "/verify-email.php?token=" . $token;
    
    $body = "
    <h2>नमस्ते {$name},</h2>
    <p>" . SITE_NAME . " में आपका स्वागत है!</p>
    <p>कृपया अपना ईमेल वेरिफाई करने के लिए नीचे दिए गए लिंक पर क्लिक करें:</p>
    <p><a href='{$verification_link}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ईमेल वेरिफाई करें</a></p>
    <p>यदि आपने यह अकाउंट नहीं बनाया है, तो कृपया इस ईमेल को अनदेखा करें।</p>
    ";
    
    return send_email($email, $subject, $body);
}

function send_contact_thank_you_email($email, $name) {
    $subject = "आपके संदेश के लिए धन्यवाद - " . SITE_NAME;
    
    $body = "
    <h2>नमस्ते {$name},</h2>
    <p>आपके संदेश के लिए धन्यवाद। हमारी टीम जल्द ही आपसे संपर्क करेगी।</p>
    <p>यदि आपका कोई तत्काल प्रश्न है, तो आप हमें " . CONTACT_PHONE . " पर कॉल कर सकते हैं।</p>
    <p>धन्यवाद,<br>" . SITE_NAME . " टीम</p>
    ";
    
    return send_email($email, $subject, $body);
}

function send_contact_notification_email($data) {
    $subject = "नई संपर्क पूछताछ - " . SITE_NAME;
    
    $body = "
    <h2>नई संपर्क पूछताछ प्राप्त हुई</h2>
    <p><strong>नाम:</strong> {$data['name']}</p>
    <p><strong>ईमेल:</strong> {$data['email']}</p>
    <p><strong>फोन:</strong> " . ($data['phone'] ?? 'नहीं दिया गया') . "</p>
    <p><strong>सेवा:</strong> " . ($data['service'] ?? 'नहीं चुनी गई') . "</p>
    <p><strong>संदेश:</strong><br>" . nl2br($data['message']) . "</p>
    <p><strong>IP Address:</strong> " . $_SERVER['REMOTE_ADDR'] . "</p>
    <p><strong>समय:</strong> " . date('d M Y, h:i A') . "</p>
    ";
    
    return send_email(ADMIN_EMAIL, $subject, $body);
}

// Blog Functions

function get_blog_posts($limit = null, $category_id = null, $status = 'published') {
    global $pdo;
    
    try {
        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name 
                FROM blog_posts p 
                LEFT JOIN users u ON p.author_id = u.id 
                LEFT JOIN blog_categories c ON p.category_id = c.id 
                WHERE p.status = ?";
        
        $params = [$status];
        
        if ($category_id) {
            $sql .= " AND p.category_id = ?";
            $params[] = $category_id;
        }
        
        $sql .= " ORDER BY p.published_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get blog posts error: " . $e->getMessage());
        return [];
    }
}

function get_blog_post_by_slug($slug) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT p.*, u.first_name, u.last_name, c.name as category_name 
                              FROM blog_posts p 
                              LEFT JOIN users u ON p.author_id = u.id 
                              LEFT JOIN blog_categories c ON p.category_id = c.id 
                              WHERE p.slug = ? AND p.status = 'published'");
        $stmt->execute([$slug]);
        $post = $stmt->fetch();
        
        if ($post) {
            // Increment views
            $stmt = $pdo->prepare("UPDATE blog_posts SET views = views + 1 WHERE id = ?");
            $stmt->execute([$post['id']]);
        }
        
        return $post;
        
    } catch (PDOException $e) {
        error_log("Get blog post error: " . $e->getMessage());
        return false;
    }
}

// Portfolio Functions

function get_portfolio_items($limit = null, $category = null) {
    global $pdo;
    
    try {
        $sql = "SELECT * FROM portfolio_items WHERE status = 'active'";
        $params = [];
        
        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        $sql .= " ORDER BY featured DESC, sort_order ASC, completion_date DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get portfolio items error: " . $e->getMessage());
        return [];
    }
}

// Utility Functions

function get_setting($key, $default = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value, setting_type FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $setting = $stmt->fetch();
        
        if (!$setting) {
            return $default;
        }
        
        switch ($setting['setting_type']) {
            case 'boolean':
                return filter_var($setting['setting_value'], FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($setting['setting_value']) ? (float)$setting['setting_value'] : $default;
            case 'json':
                return json_decode($setting['setting_value'], true) ?: $default;
            default:
                return $setting['setting_value'];
        }
        
    } catch (PDOException $e) {
        error_log("Get setting error: " . $e->getMessage());
        return $default;
    }
}

function update_setting($key, $value, $type = 'string') {
    global $pdo;
    
    try {
        if ($type === 'boolean') {
            $value = $value ? 'true' : 'false';
        } elseif ($type === 'json') {
            $value = json_encode($value);
        }
        
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = ?, setting_type = ?, updated_at = NOW()");
        $stmt->execute([$key, $value, $type, $value, $type]);
        
        return true;
        
    } catch (PDOException $e) {
        error_log("Update setting error: " . $e->getMessage());
        return false;
    }
}

// reCAPTCHA Verification
function verify_recaptcha($response) {
    if (!RECAPTCHA_SECRET_KEY) {
        return true; // Skip verification if not configured
    }
    
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => RECAPTCHA_SECRET_KEY,
        'response' => $response,
        'remoteip' => $_SERVER['REMOTE_ADDR']
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    $response_data = json_decode($result, true);
    
    return isset($response_data['success']) && $response_data['success'] === true;
}
?>
