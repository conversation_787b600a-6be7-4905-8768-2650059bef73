<header class="main-header">
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span><i class="fas fa-phone"></i> <?php echo CONTACT_PHONE; ?></span>
                        <span><i class="fas fa-envelope"></i> <?php echo SITE_EMAIL; ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="social-links text-end">
                        <?php if(FACEBOOK_URL): ?>
                        <a href="<?php echo FACEBOOK_URL; ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <?php endif; ?>
                        <?php if(TWITTER_URL): ?>
                        <a href="<?php echo TWITTER_URL; ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                        <?php endif; ?>
                        <?php if(INSTAGRAM_URL): ?>
                        <a href="<?php echo INSTAGRAM_URL; ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <?php endif; ?>
                        <?php if(LINKEDIN_URL): ?>
                        <a href="<?php echo LINKEDIN_URL; ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                        <?php endif; ?>
                        <?php if(is_logged_in()): ?>
                        <a href="dashboard/" class="btn btn-sm btn-outline-light ms-2">डैशबोर्ड</a>
                        <a href="logout.php" class="btn btn-sm btn-light ms-1">लॉगआउट</a>
                        <?php else: ?>
                        <a href="login.php" class="btn btn-sm btn-outline-light ms-2">लॉगिन</a>
                        <a href="register.php" class="btn btn-sm btn-light ms-1">रजिस्टर</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>" height="50">
                <span class="brand-text"><?php echo SITE_NAME; ?></span>
            </a>

            <!-- Mobile Menu Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">होम</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : ''; ?>" href="services.php">सेवाएं</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'portfolio.php' ? 'active' : ''; ?>" href="portfolio.php">पोर्टफोलियो</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="blogDropdown" role="button" data-bs-toggle="dropdown">
                            ब्लॉग
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="blog/">सभी आर्टिकल्स</a></li>
                            <li><a class="dropdown-item" href="blog/category/web-development">वेब डेवलपमेंट</a></li>
                            <li><a class="dropdown-item" href="blog/category/digital-marketing">डिजिटल मार्केटिंग</a></li>
                            <li><a class="dropdown-item" href="blog/category/seo">SEO</a></li>
                            <li><a class="dropdown-item" href="blog/category/ecommerce">ई-कॉमर्स</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'about.php' ? 'active' : ''; ?>" href="about.php">हमारे बारे में</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>" href="contact.php">संपर्क</a>
                    </li>
                    <?php if(is_logged_in()): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $_SESSION['full_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard/">डैशबोर्ड</a></li>
                            <li><a class="dropdown-item" href="dashboard/profile.php">प्रोफाइल</a></li>
                            <li><a class="dropdown-item" href="dashboard/projects.php">मेरे प्रोजेक्ट्स</a></li>
                            <li><a class="dropdown-item" href="dashboard/orders.php">मेरे ऑर्डर्स</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <!-- CTA Button -->
                <div class="ms-3">
                    <a href="#contact" class="btn btn-primary">मुफ्त सलाह लें</a>
                </div>
            </div>
        </div>
    </nav>
</header>

<!-- Alert Messages -->
<div class="container mt-3">
    <?php show_alert(); ?>
</div>

<!-- Breadcrumb (for inner pages) -->
<?php if(basename($_SERVER['PHP_SELF']) != 'index.php'): ?>
<div class="breadcrumb-section">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">होम</a></li>
                <?php
                $current_page = basename($_SERVER['PHP_SELF'], '.php');
                $page_titles = [
                    'services' => 'सेवाएं',
                    'portfolio' => 'पोर्टफोलियो',
                    'blog' => 'ब्लॉग',
                    'about' => 'हमारे बारे में',
                    'contact' => 'संपर्क',
                    'login' => 'लॉगिन',
                    'register' => 'रजिस्टर'
                ];
                
                if(isset($page_titles[$current_page])):
                ?>
                <li class="breadcrumb-item active"><?php echo $page_titles[$current_page]; ?></li>
                <?php endif; ?>
            </ol>
        </nav>
    </div>
</div>
<?php endif; ?>
