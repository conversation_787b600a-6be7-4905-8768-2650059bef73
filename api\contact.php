<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

// Start session
session_start();

try {
    // Get form data
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $service = sanitize_input($_POST['service'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'नाम आवश्यक है';
    }
    
    if (empty($email)) {
        $errors[] = 'ईमेल आवश्यक है';
    } elseif (!validate_email($email)) {
        $errors[] = 'वैध ईमेल एड्रेस दर्ज करें';
    }
    
    if (empty($phone)) {
        $errors[] = 'फोन नंबर आवश्यक है';
    } elseif (!validate_phone($phone)) {
        $errors[] = 'वैध फोन नंबर दर्ज करें';
    }
    
    if (empty($message)) {
        $errors[] = 'संदेश आवश्यक है';
    }
    
    // Verify reCAPTCHA if enabled
    if (defined('RECAPTCHA_SECRET_KEY') && RECAPTCHA_SECRET_KEY) {
        if (empty($recaptcha_response)) {
            $errors[] = 'कृपया reCAPTCHA वेरिफाई करें';
        } elseif (!verify_recaptcha($recaptcha_response)) {
            $errors[] = 'reCAPTCHA वेरिफिकेशन असफल';
        }
    }
    
    // Check for errors
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => implode(', ', $errors)
        ]);
        exit;
    }
    
    // Rate limiting - check if same IP has submitted recently
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contact_inquiries WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
    $stmt->execute([$ip_address]);
    $recent_submissions = $stmt->fetchColumn();
    
    if ($recent_submissions >= 3) {
        echo json_encode([
            'success' => false,
            'message' => 'बहुत सारे अनुरोध। कृपया 5 मिनट बाद पुनः प्रयास करें।'
        ]);
        exit;
    }
    
    // Prepare data for saving
    $contact_data = [
        'name' => $name,
        'email' => $email,
        'phone' => $phone,
        'service' => $service,
        'message' => $message
    ];
    
    // Save contact inquiry
    $result = save_contact_inquiry($contact_data);
    
    if ($result['success']) {
        // Log the inquiry
        error_log("New contact inquiry from: {$name} ({$email})");
        
        echo json_encode([
            'success' => true,
            'message' => 'आपका संदेश सफलतापूर्वक भेज दिया गया है। हम जल्द ही आपसे संपर्क करेंगे।'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['message'] ?? 'संदेश भेजने में त्रुटि हुई'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'सर्वर त्रुटि। कृपया बाद में पुनः प्रयास करें।'
    ]);
}
?>
