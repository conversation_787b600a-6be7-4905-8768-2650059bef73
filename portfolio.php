<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

$page_title = "पोर्टफोलियो - हमारे काम के नमूने";
$meta_description = "हमारे द्वारा बनाई गई वेबसाइट्स और प्रोजेक्ट्स देखें। Business websites, E-commerce stores, और Custom applications के examples।";
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $meta_description; ?>">
    <meta name="keywords" content="पोर्टफोलियो, वेब डेवलपमेंट examples, ई-कॉमर्स websites, business websites">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>हमारा पोर्टफोलियो</h1>
                    <p class="lead">हमारे द्वारा बनाए गए successful projects देखें</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Portfolio Filter -->
    <section class="portfolio-filter">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="filter-buttons text-center">
                        <button class="filter-btn active" data-filter="all">सभी</button>
                        <button class="filter-btn" data-filter="business">Business Websites</button>
                        <button class="filter-btn" data-filter="ecommerce">E-commerce</button>
                        <button class="filter-btn" data-filter="custom">Custom Development</button>
                        <button class="filter-btn" data-filter="mobile">Mobile Apps</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Portfolio Grid -->
    <section class="portfolio-grid">
        <div class="container">
            <div class="row" id="portfolioContainer">
                <!-- Business Website 1 -->
                <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="business">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="assets/images/portfolio/business-1.jpg" alt="Restaurant Website" class="img-fluid">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="assets/images/portfolio/business-1.jpg" class="btn btn-light btn-sm" data-lightbox="portfolio">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <h4>रेस्टोरेंट वेबसाइट</h4>
                            <p>एक modern restaurant के लिए complete website with online menu और booking system</p>
                            <div class="portfolio-tags">
                                <span class="tag">Business Premium</span>
                                <span class="tag">Restaurant</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- E-commerce 1 -->
                <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="ecommerce">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="assets/images/portfolio/ecommerce-1.jpg" alt="Fashion Store" class="img-fluid">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="assets/images/portfolio/ecommerce-1.jpg" class="btn btn-light btn-sm" data-lightbox="portfolio">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <h4>फैशन ई-कॉमर्स स्टोर</h4>
                            <p>Complete online fashion store with payment gateway, inventory management और order tracking</p>
                            <div class="portfolio-tags">
                                <span class="tag">E-commerce</span>
                                <span class="tag">Fashion</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Custom Development 1 -->
                <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="custom">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="assets/images/portfolio/custom-1.jpg" alt="School Management System" class="img-fluid">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="assets/images/portfolio/custom-1.jpg" class="btn btn-light btn-sm" data-lightbox="portfolio">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <h4>स्कूल मैनेजमेंट सिस्टम</h4>
                            <p>Complete school management system with student, teacher और parent portals</p>
                            <div class="portfolio-tags">
                                <span class="tag">Custom</span>
                                <span class="tag">Education</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Business Website 2 -->
                <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="business">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="assets/images/portfolio/business-2.jpg" alt="Real Estate Website" class="img-fluid">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="assets/images/portfolio/business-2.jpg" class="btn btn-light btn-sm" data-lightbox="portfolio">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <h4>रियल एस्टेट वेबसाइट</h4>
                            <p>Property listing website with advanced search और inquiry management system</p>
                            <div class="portfolio-tags">
                                <span class="tag">Business Premium</span>
                                <span class="tag">Real Estate</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- E-commerce 2 -->
                <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="ecommerce">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="assets/images/portfolio/ecommerce-2.jpg" alt="Electronics Store" class="img-fluid">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="assets/images/portfolio/ecommerce-2.jpg" class="btn btn-light btn-sm" data-lightbox="portfolio">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <h4>इलेक्ट्रॉनिक्स स्टोर</h4>
                            <p>Multi-vendor electronics marketplace with comparison features और reviews</p>
                            <div class="portfolio-tags">
                                <span class="tag">E-commerce</span>
                                <span class="tag">Electronics</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Mobile App -->
                <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="mobile">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="assets/images/portfolio/mobile-1.jpg" alt="Food Delivery App" class="img-fluid">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="assets/images/portfolio/mobile-1.jpg" class="btn btn-light btn-sm" data-lightbox="portfolio">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <h4>फूड डिलीवरी ऐप</h4>
                            <p>Complete food delivery mobile app with real-time tracking और payment integration</p>
                            <div class="portfolio-tags">
                                <span class="tag">Mobile App</span>
                                <span class="tag">Food</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Load More Button -->
            <div class="row">
                <div class="col-12 text-center">
                    <button class="btn btn-outline-primary btn-lg" id="loadMoreBtn">
                        <i class="fas fa-plus"></i> और देखें
                    </button>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Client Testimonials -->
    <section class="testimonials-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>ग्राहकों की राय</h2>
                <p>हमारे clients क्या कहते हैं</p>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"बहुत ही professional team है। समय पर काम पूरा किया और quality भी excellent है। हमारी sales 300% बढ़ गई है।"</p>
                        </div>
                        <div class="testimonial-author">
                            <img src="assets/images/testimonials/client-1.jpg" alt="Client 1" class="author-image">
                            <div class="author-info">
                                <h5>राज शर्मा</h5>
                                <span>Fashion Store Owner</span>
                            </div>
                        </div>
                        <div class="testimonial-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"Amazing work! हमारी restaurant की website बनाने के बाद online orders में 250% की वृद्धि हुई है।"</p>
                        </div>
                        <div class="testimonial-author">
                            <img src="assets/images/testimonials/client-2.jpg" alt="Client 2" class="author-image">
                            <div class="author-info">
                                <h5>प्रिया गुप्ता</h5>
                                <span>Restaurant Owner</span>
                            </div>
                        </div>
                        <div class="testimonial-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"School management system बहुत ही user-friendly है। सभी teachers और parents को use करने में आसानी हो रही है।"</p>
                        </div>
                        <div class="testimonial-author">
                            <img src="assets/images/testimonials/client-3.jpg" alt="Client 3" class="author-image">
                            <div class="author-info">
                                <h5>डॉ. अमित कुमार</h5>
                                <span>School Principal</span>
                            </div>
                        </div>
                        <div class="testimonial-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2>अपना प्रोजेक्ट शुरू करने के लिए तैयार हैं?</h2>
                    <p>हमसे संपर्क करें और अपने business को digital world में सफल बनाएं</p>
                    <div class="cta-buttons">
                        <a href="services.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket"></i> अभी शुरू करें
                        </a>
                        <a href="contact.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone"></i> संपर्क करें
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <!-- Portfolio Filter Script -->
    <script>
    // Portfolio Filter
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                const filterValue = this.getAttribute('data-filter');
                
                portfolioItems.forEach(item => {
                    if (filterValue === 'all' || item.getAttribute('data-category') === filterValue) {
                        item.style.display = 'block';
                        item.classList.add('animate-fadeIn');
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
        
        // Load More functionality
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                // Simulate loading more items
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> लोड हो रहा है...';
                
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> सभी प्रोजेक्ट्स लोड हो गए';
                    this.disabled = true;
                }, 2000);
            });
        }
    });
    </script>
    
    <!-- WhatsApp Float Button -->
    <div class="whatsapp-float">
        <a href="https://wa.me/<?php echo CONTACT_WHATSAPP; ?>?text=नमस्ते! मुझे आपके portfolio के बारे में जानकारी चाहिए।" target="_blank">
            <i class="fab fa-whatsapp"></i>
        </a>
    </div>
</body>
</html>
